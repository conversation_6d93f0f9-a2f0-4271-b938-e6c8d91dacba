'use client';

import { Footer } from '@/components/Footer';
import { EnhancedHubCard } from '@/components/hubs/EnhancedHubCard';
import { FeaturedHubsSection } from '@/components/hubs/FeaturedHubsSection';
import { HeroSection } from '@/components/hubs/HeroSection';
import { LeaderboardSection } from '@/components/hubs/LeaderboardSection';
import { SearchBar } from '@/components/hubs/SearchBar';
import { StatsBar } from '@/components/hubs/StatsBar';
import { Button } from '@/components/ui/button';
import { useInfiniteHubs } from '@/hooks/use-infinite-hubs';
import { Search } from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import React, { useState } from 'react';
import type { LeaderboardResponse, FeaturedHubsResponse } from '@/lib/platform-stats';

interface StatsData {
  activeServers: number;
  publicHubs: number;
  weeklyMessages: number;
}

interface RedesignedHubsPageProps {
  initialStats?: StatsData;
  initialLeaderboard?: LeaderboardResponse;
  initialFeaturedHubs?: FeaturedHubsResponse;
}

/**
 * Redesigned Hubs Page with new layout structure
 * Implements the complete design specification with all sections
 */
export function HubsPage({ initialStats, initialLeaderboard, initialFeaturedHubs }: RedesignedHubsPageProps) {
  const [isFiltersSticky, setIsFiltersSticky] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Use initial stats from SSR instead of client-side fetch
  const stats = initialStats;

  // Fetch hubs based on current filters
  const {
    hubs,
    hasMore,
    isLoading: hubsLoading,
  } = useInfiniteHubs({
    search: searchTerm,
    limit: 12,
  });

  // Handle scroll to make filters sticky
  React.useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const heroHeight = window.innerHeight * 0.8; // Approximate hero height
      setIsFiltersSticky(scrollY > heroHeight);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-primary">
      {/* Enhanced background layers */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        {/* Primary gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-900/95 to-gray-950" />

        {/* Radial gradient overlays for depth */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/10 via-transparent to-blue-900/10" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />

        {/* Enhanced grid pattern */}
        <div className="absolute inset-0 bg-grid-white bg-[size:60px_60px] opacity-[0.02] [mask-image:radial-gradient(ellipse_80%_80%_at_50%_50%,#000_20%,transparent_120%)]" />
      </div>

      {/* Content with relative positioning */}
      <div className="relative z-10">
        {/* 1️⃣ Enhanced Hero Section (Top) */}
        <HeroSection />

        {/* 2️⃣ Enhanced Stats Bar (below hero) */}
        <StatsBar stats={stats} />

        {/* Enhanced Mobile Sticky Search Bar */}
        {isFiltersSticky && (
          <div className="sticky top-0 z-50 bg-gray-900/95 backdrop-blur-xl border-b border-white/10 lg:hidden">
            <div className="container mx-auto px-4 py-3">
              <div className="relative">
                <SearchBar
                  initialValue={searchTerm}
                  placeholder="Search hubs..."
                  size="default"
                  variant="header"
                  showButton={false}
                  autoFocus={false}
                  onSearch={setSearchTerm}
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>
          </div>
        )}

        {/* 3️⃣ Enhanced Featured Hubs / Staff Picks */}
        <FeaturedHubsSection initialData={initialFeaturedHubs} />

        {/* 4️⃣ Enhanced Hub Listings Grid (Main content) */}
        <div className="py-12 relative">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-12"
          >
            <h2 className="text-3xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-3">All Hubs</h2>
            <p className="text-gray-400 text-lg">Discover communities that match your interests</p>
          </motion.div>

          {/* Enhanced Hub Cards Grid */}
          {hubsLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-96 premium-card animate-pulse">
                  <div className="p-6 space-y-4">
                    <div className="h-4 bg-gray-700/50 rounded animate-shimmer" />
                    <div className="h-8 bg-gray-700/50 rounded animate-shimmer" />
                    <div className="h-4 bg-gray-700/50 rounded w-3/4 animate-shimmer" />
                  </div>
                </div>
              ))}
            </div>
          ) : hubs.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                {hubs.map((hub, index) => (
                  <EnhancedHubCard key={hub.id} hub={hub} index={index} />
                ))}
              </div>

              {hasMore && (
                <div className="text-center space-y-4">
                  <Link href="/hubs/search">
                    <Button
                      variant="outline"
                      size="lg"
                      className="cursor-pointer text-lg px-8 py-4 h-14"
                    >
                      Load More Hubs
                    </Button>
                  </Link>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-20">
              <div className="text-8xl mb-6 opacity-50">🔍</div>
              <h3 className="text-2xl font-semibold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-4">No hubs found</h3>
              <p className="text-gray-400 mb-8 text-lg max-w-md mx-auto leading-relaxed">
                Try adjusting your filters or search terms to find more communities.
              </p>
            </div>
          )}
        </div>
        </div>

        {/* 5️⃣ Enhanced Leaderboard Section (Competition) */}
        <LeaderboardSection initialData={initialLeaderboard} />

        {/* 7️⃣ Enhanced Footer */}
        <Footer />
      </div>
    </div>
  );
}
