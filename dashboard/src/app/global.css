@import "tailwindcss";
@import "fumadocs-ui/css/neutral.css";
@import "fumadocs-ui/css/preset.css";

@source '../../node_modules/fumadocs-ui/dist/**/*.js';

@layer base {
  :root {
    /* Enhanced Rounded Design System */
    --radius: 0.5rem; /* 8px - Base radius for cards */
    --radius-button: 0.75rem; /* 12px - Enhanced button radius */
    --radius-modal: 1rem; /* 16px - Modal and dialog radius */
    --radius-input: 0.5rem; /* 8px - Input field radius */
    --radius-badge: 9999px; /* Full rounded for badges */
    --radius-avatar: 9999px; /* Full rounded for avatars */
    --radius-sm: 0.375rem; /* 6px - Small radius for skeleton and small elements */
    --radius-xs: 0.25rem; /* 4px - Extra small radius for checkboxes */

    /* Enhanced Color System - Premium Design Tokens */
    /* Primary Brand Colors */
    --color-brand-purple-50: #f8f6ff;
    --color-brand-purple-100: #ede9ff;
    --color-brand-purple-200: #ddd6ff;
    --color-brand-purple-300: #c4b5ff;
    --color-brand-purple-400: #a78bff;
    --color-brand-purple-500: #9172d8;
    --color-brand-purple-600: #7c5ce0;
    --color-brand-purple-700: #6b46c1;
    --color-brand-purple-800: #553c9a;
    --color-brand-purple-900: #44337a;

    /* Secondary Brand Colors */
    --color-brand-blue-400: #60a5fa;
    --color-brand-blue-500: #3b82f6;
    --color-brand-blue-600: #2563eb;
    --color-brand-indigo-400: #818cf8;
    --color-brand-indigo-500: #6366f1;
    --color-brand-indigo-600: #4f46e5;
    --color-brand-pink-400: #f472b6;
    --color-brand-pink-500: #ec4899;
    --color-brand-pink-600: #db2777;
    --color-brand-emerald-400: #34d399;
    --color-brand-emerald-500: #10b981;
    --color-brand-emerald-600: #059669;
    --color-brand-orange-400: #fb923c;
    --color-brand-orange-500: #f97316;
    --color-brand-orange-600: #ea580c;

    /* Enhanced Background System */
    --color-bg-primary: #0a0a0c;
    --color-bg-secondary: #111827;
    --color-bg-tertiary: #1f2937;
    --color-bg-card: rgba(17, 24, 39, 0.8);
    --color-bg-card-hover: rgba(31, 41, 55, 0.8);

    /* Glass Morphism Effects */
    --glass-bg-light: rgba(255, 255, 255, 0.05);
    --glass-bg-medium: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.3);

    /* Enhanced Shadow System */
    --shadow-glow-purple: 0 0 20px rgba(147, 114, 216, 0.3);
    --shadow-glow-blue: 0 0 20px rgba(59, 130, 246, 0.3);
    --shadow-glow-indigo: 0 0 20px rgba(99, 102, 241, 0.3);
    --shadow-glow-pink: 0 0 20px rgba(236, 72, 153, 0.3);
    --shadow-glow-emerald: 0 0 20px rgba(16, 185, 129, 0.3);
    --shadow-glow-orange: 0 0 20px rgba(249, 115, 22, 0.3);

    /* Premium Gradient Definitions */
    --gradient-purple: linear-gradient(135deg, rgba(147, 114, 216, 0.2) 0%, rgba(124, 92, 224, 0.1) 50%, rgba(147, 114, 216, 0.2) 100%);
    --gradient-blue: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(6, 182, 212, 0.1) 50%, rgba(59, 130, 246, 0.2) 100%);
    --gradient-indigo: linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(59, 130, 246, 0.1) 50%, rgba(99, 102, 241, 0.2) 100%);
    --gradient-pink: linear-gradient(135deg, rgba(236, 72, 153, 0.2) 0%, rgba(251, 113, 133, 0.1) 50%, rgba(236, 72, 153, 0.2) 100%);
    --gradient-emerald: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(20, 184, 166, 0.1) 50%, rgba(16, 185, 129, 0.2) 100%);
    --gradient-orange: linear-gradient(135deg, rgba(249, 115, 22, 0.2) 0%, rgba(245, 158, 11, 0.1) 50%, rgba(249, 115, 22, 0.2) 100%);

    /* Enhanced Background Gradients */
    --bg-gradient-primary: radial-gradient(circle at 20% 20%, rgba(147, 114, 216, 0.1) 0%, transparent 50%), linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
    --bg-gradient-secondary: radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), linear-gradient(135deg, rgba(17, 24, 39, 0.9) 0%, rgba(31, 41, 55, 0.9) 100%);
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  /* Mobile-friendly tab scrolling */
  .mobile-tab-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Ensure tabs don't break on mobile */
  .mobile-tab-container {
    min-width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
  }

  /* Better touch targets on mobile */
  @media (max-width: 640px) {
    .mobile-tab-trigger {
      min-height: 44px; /* iOS recommended touch target */
      min-width: 44px;
    }

    /* Improve connections page mobile experience */
    .connections-card {
      margin-left: -1rem;
      margin-right: -1rem;
      border-radius: 0;
      border-left: none;
      border-right: none;
    }

    /* Better spacing for mobile connection items */
    .connection-item-mobile {
      padding: 1rem;
    }

    /* Ensure proper touch targets for buttons */
    .touch-manipulation {
      touch-action: manipulation;
      -webkit-tap-highlight-color: transparent;
    }
  }
}

:root {
  --color-fd-primary: var(--color-brand-purple-500);
}

@theme {
  /* Enhanced Background System */
  --color-background: var(--color-bg-primary);
  --color-primary: var(--color-brand-purple-500);
  --color-primary-alt: var(--color-brand-blue-500);
  --color-border: rgba(55, 65, 81, 0.5);
  --color-input: rgba(31, 41, 55, 0.8);
  --color-ring: var(--color-brand-purple-400);
  --color-foreground: hsl(210 40% 98%);

  /* Enhanced Card System */
  --color-card: var(--color-bg-card);
  --color-card-foreground: hsl(210 40% 98%);

  /* Enhanced Popover System */
  --color-popover: rgba(17, 24, 39, 0.95);
  --color-popover-foreground: hsl(210 40% 98%);

  --color-primary-foreground: hsl(210 40% 98%);

  /* Enhanced Secondary Colors */
  --color-secondary: rgba(31, 41, 55, 0.8);
  --color-secondary-foreground: hsl(210 40% 98%);

  /* Enhanced Muted Colors */
  --color-muted: rgba(31, 41, 55, 0.6);
  --color-muted-foreground: hsl(215 20.2% 65.1%);

  /* Enhanced Accent Colors */
  --color-accent: rgba(31, 41, 55, 0.8);
  --color-accent-foreground: hsl(210 40% 98%);

  /* Enhanced Destructive Colors */
  --color-destructive: hsl(0 62.8% 50%);
  --color-destructive-foreground: hsl(210 40% 98%);

  /* Enhanced radius system for consistent rounded design */
  --radius-lg: var(--radius-modal); /* 16px for large elements like modals */
  --radius-md: var(--radius-button); /* 12px for medium elements like buttons */
  --radius-sm: var(--radius); /* 8px for small elements like inputs */
  --radius-xs: calc(var(--radius) - 2px); /* 6px for extra small elements */

  /* Enhanced Animation System */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-shiny-text: shiny-text 8s infinite;
  --animate-gradient: gradient 8s linear infinite;
  --animate-float: float 6s ease-in-out infinite;
  --animate-glow: glow 2s ease-in-out infinite alternate;

  @keyframes gradient {
    0%, 100% {
      background-size: 200% 200%;
      background-position: left center;
    }
    50% {
      background-size: 200% 200%;
      background-position: right center;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes glow {
    0% {
      box-shadow: 0 0 5px rgba(147, 114, 216, 0.2);
    }
    100% {
      box-shadow: 0 0 20px rgba(147, 114, 216, 0.4);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
}

/* Enhanced Animation Utility Classes */
.animate-gradient-slow {
  animation: var(--animate-gradient);
}

.animate-float {
  animation: var(--animate-float);
}

.animate-glow {
  animation: var(--animate-glow);
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
}

/* Enhanced Background Utility Classes */
.bg-mesh-gradient {
  background-image: radial-gradient(at 40% 20%, hsla(28, 100%, 74%, 1) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 1) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(355, 100%, 93%, 1) 0px, transparent 50%),
    radial-gradient(at 80% 50%, hsla(340, 100%, 76%, 1) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(22, 100%, 77%, 1) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(242, 100%, 70%, 1) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343, 100%, 76%, 1) 0px, transparent 50%);
}

.bg-gradient-primary {
  background: var(--bg-gradient-primary);
}

.bg-gradient-secondary {
  background: var(--bg-gradient-secondary);
}

/* Glass Morphism Utility Classes */
.glass-card {
  background: var(--glass-bg-light);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px var(--glass-shadow);
}

.glass-card-medium {
  background: var(--glass-bg-medium);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px var(--glass-shadow);
}

/* Enhanced Card Utility Classes */
.premium-card {
  background: var(--color-bg-card);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card:hover {
  background: var(--color-bg-card-hover);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  transform: translateY(-2px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Remove most fade-in animations to improve performance */
.animate-fade-in {
  opacity: 1;
  transform: translateY(0);
}

.animate-fade-in-delayed {
  opacity: 1;
  transform: translateY(0);
}

.animate-fade-in-more-delayed {
  opacity: 1;
  transform: translateY(0);
}

.animate-fade-in-staggered {
  opacity: 1;
  transform: translateY(0);
}

/* Ensure dropdowns and popovers appear on top of other elements */
.dropdown-menu-content,
[data-radix-popper-content-wrapper],
[cmdk-root],
[cmdk-list],
[cmdk-item],
[role="dialog"],
[role="menu"],
[role="listbox"],
[role="combobox"] {
  z-index: 9999 !important;
}

/* Ensure hub search dropdown appears above all other elements */
.hub-search-dropdown {
  z-index: 10000 !important;
}

/* Command component styling improvements */
[cmdk-root] {
  background: rgba(31, 41, 55, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(55, 65, 81, 0.5) !important;
}

[cmdk-group-heading] {
  color: #9ca3af !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  padding: 8px 12px 4px !important;
}
