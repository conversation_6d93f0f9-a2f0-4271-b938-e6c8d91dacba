import { auth } from "@/auth";
import { DashboardLayoutProvider } from "@/components/dashboard/layout-provider";
import { GuidedTourProvider } from "@/components/dashboard/onboarding/guided-tour-provider";
import { DashboardTopBar } from "@/components/dashboard/topbar";
import { redirect } from "next/navigation";
import type { ReactNode } from "react";
import "./dashboard.css";

export default async function DashboardLayout({
  children,
}: {
  children: ReactNode;
}) {
  const session = await auth();

  // Redirect to login if not authenticated
  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard");
  }

  return (
    <DashboardLayoutProvider>
        <GuidedTourProvider>
          <div className="min-h-screen bg-gradient-primary">
            {/* Enhanced dashboard layout with premium background */}
            <div className="flex flex-col h-screen overflow-hidden">
              {/* Top bar */}
              <DashboardTopBar user={session.user} />

              {/* Main content area with enhanced background */}
              <main className="flex-1 overflow-y-auto dashboard-scrollbar relative">
                {/* Enhanced background layers */}
                <div className="fixed inset-0 z-0 pointer-events-none">
                  {/* Primary gradient background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-900/95 to-gray-950" />

                  {/* Radial gradient overlays for depth */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-900/10 via-transparent to-blue-900/10" />
                  <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl" />
                  <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />

                  {/* Enhanced grid pattern */}
                  <div
                    className="absolute inset-0 bg-grid-white bg-[size:40px_40px] opacity-[0.02] [mask-image:radial-gradient(ellipse_80%_80%_at_50%_50%,#000_20%,transparent_120%)]"
                    style={{ zIndex: -1 }}
                  />

                  {/* Subtle noise texture */}
                  <div className="absolute inset-0 opacity-[0.015] bg-[url('/noise.svg')] bg-repeat" />
                </div>

                {/* Content with enhanced spacing */}
                <div className="relative z-10 p-6">
                  {children}
                </div>
              </main>
            </div>
          </div>
        </GuidedTourProvider>
    </DashboardLayoutProvider>
  );
}
