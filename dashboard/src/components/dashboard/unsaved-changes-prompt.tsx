"use client";

import { But<PERSON> } from '@/components/ui/button';
import { motion, AnimatePresence } from 'motion/react';
import { AlertTriangle, RotateCcw, Save, Loader2 } from 'lucide-react';

interface UnsavedChangesPromptProps {
  isVisible: boolean;
  onSave: (e: React.FormEvent) => Promise<void> | void;
  onReset: () => void;
  isSubmitting?: boolean;
  saveLabel?: string;
  resetLabel?: string;
  message?: string;
}

export function UnsavedChangesPrompt({
  isVisible,
  onSave,
  onReset,
  isSubmitting = false,
  saveLabel = "Save Changes",
  resetLabel = "Reset",
  message = "Careful - you have unsaved changes!",
}: UnsavedChangesPromptProps) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 20, scale: 0.95 }}
          transition={{ 
            type: "spring", 
            stiffness: 400, 
            damping: 25,
            duration: 0.2
          }}
          className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50"
        >
          <div 
            className="flex items-center gap-3 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg shadow-xl"
            style={{
              boxShadow: `
                0 8px 16px -4px rgba(0, 0, 0, 0.4),
                0 4px 6px -2px rgba(0, 0, 0, 0.2)
              `
            }}
          >
            {/* Icon and Message */}
            <div className="flex items-center gap-2.5">
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, type: "spring", stiffness: 300 }}
              >
                <AlertTriangle className="h-4 w-4 text-orange-400" />
              </motion.div>
              
              <motion.span
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
                className="text-white text-sm font-medium whitespace-nowrap"
              >
                {message}
              </motion.span>
            </div>

            {/* Divider */}
            <div className="w-px h-5 bg-gray-600" />

            {/* Actions */}
            <motion.div
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.15 }}
              className="flex items-center gap-2"
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={onReset}
                disabled={isSubmitting}
                className="text-gray-300 hover:text-white hover:bg-gray-700 px-3 py-1.5 h-auto text-xs font-medium transition-colors duration-150 flex items-center gap-1.5"
              >
                <RotateCcw className="h-3 w-3" />
                {resetLabel}
              </Button>
              
              <Button
                size="sm"
                onClick={(e) => onSave(e)}
                disabled={isSubmitting}
                className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1.5 h-auto text-xs font-medium transition-colors duration-150 flex items-center gap-1.5"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-3 w-3 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-3 w-3" />
                    {saveLabel}
                  </>
                )}
              </Button>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}