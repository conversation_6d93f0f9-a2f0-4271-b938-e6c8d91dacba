'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import {
  AlertTriangle,
  Bell,
  ChevronLeft,
  ChevronRight,
  Edit,
  FileText,
  Gavel,
  Globe,
  Home,
  MessageSquare,
  Settings,
  Shield,
  Users,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface HubSidebarProps {
  hubId: string;
  canModerate?: boolean;
  canEdit?: boolean;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

interface SidebarNavItemProps {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  active: boolean;
  isCollapsed: boolean;
  color?: 'default' | 'blue' | 'green' | 'purple' | 'red' | 'orange' | 'yellow';
}

interface NavigationItem {
  value: string;
  label: string;
  color: 'default' | 'blue' | 'green' | 'purple' | 'red' | 'orange' | 'yellow';
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  show: boolean | ((permissions: { canModerate: boolean; canEdit: boolean }) => boolean);
}

interface SidebarSection {
  key: string;
  title?: string;
  defaultOpen?: boolean;
  items: NavigationItem[];
}

function SidebarNavItem({
  href,
  icon: Icon,
  label,
  active,
  isCollapsed,
  color = 'default',
}: SidebarNavItemProps) {
  const colorClasses = {
    default: {
      active: 'bg-blue-500/10 text-blue-400 border-blue-500/20',
      inactive: 'text-gray-400 hover:text-gray-200 hover:bg-gray-800/50',
      icon: active ? 'text-blue-400' : 'text-gray-400 group-hover:text-gray-200'
    },
    blue: {
      active: 'bg-blue-500/10 text-blue-400 border-blue-500/20',
      inactive: 'text-gray-400 hover:text-gray-200 hover:bg-gray-800/50',
      icon: active ? 'text-blue-400' : 'text-gray-400 group-hover:text-gray-200'
    },
    green: {
      active: 'bg-emerald-500/10 text-emerald-400 border-emerald-500/20',
      inactive: 'text-gray-400 hover:text-gray-200 hover:bg-gray-800/50',
      icon: active ? 'text-emerald-400' : 'text-gray-400 group-hover:text-gray-200'
    },
    purple: {
      active: 'bg-purple-500/10 text-purple-400 border-purple-500/20',
      inactive: 'text-gray-400 hover:text-gray-200 hover:bg-gray-800/50',
      icon: active ? 'text-purple-400' : 'text-gray-400 group-hover:text-gray-200'
    },
    red: {
      active: 'bg-red-500/10 text-red-400 border-red-500/20',
      inactive: 'text-gray-400 hover:text-gray-200 hover:bg-gray-800/50',
      icon: active ? 'text-red-400' : 'text-gray-400 group-hover:text-gray-200'
    },
    orange: {
      active: 'bg-orange-500/10 text-orange-400 border-orange-500/20',
      inactive: 'text-gray-400 hover:text-gray-200 hover:bg-gray-800/50',
      icon: active ? 'text-orange-400' : 'text-gray-400 group-hover:text-gray-200'
    },
    yellow: {
      active: 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20',
      inactive: 'text-gray-400 hover:text-gray-200 hover:bg-gray-800/50',
      icon: active ? 'text-yellow-400' : 'text-gray-400 group-hover:text-gray-200'
    }
  };

  const colors = colorClasses[color];

  const content = (
    <Link
      href={href}
      className={cn(
        'flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group relative',
        isCollapsed ? 'justify-center' : 'gap-3',
        active
          ? `${colors.active} border shadow-sm`
          : colors.inactive
      )}
    >
      <Icon className={cn(
        'h-4 w-4 transition-colors duration-200 flex-shrink-0',
        colors.icon
      )} />
      
      {!isCollapsed && (
        <span className="truncate">
          {label}
        </span>
      )}

      {/* Simple active indicator */}
      {active && (
        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-0.5 h-4 bg-current rounded-full opacity-60" />
      )}
    </Link>
  );

  if (isCollapsed) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>{content}</TooltipTrigger>
          <TooltipContent side="right" className="ml-2">
            <p>{label}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return content;
}

interface SidebarSectionProps {
  title: string;
  children: React.ReactNode;
  isCollapsed: boolean;
  defaultOpen?: boolean;
}

function SidebarSection({ title, children, isCollapsed, defaultOpen = true }: SidebarSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  if (isCollapsed) {
    return <div className="space-y-1">{children}</div>;
  }

  return (
    <div className="space-y-2">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-2 text-xs font-semibold uppercase tracking-wider text-gray-500 hover:text-gray-300 transition-colors duration-200 group"
      >
        <span>{title}</span>
        <motion.div
          animate={{ rotate: isOpen ? 90 : 0 }}
          transition={{ duration: 0.2 }}
          className="text-gray-500 group-hover:text-gray-300"
        >
          <ChevronRight className="h-3 w-3" />
        </motion.div>
      </button>

      <motion.div
        className="overflow-hidden"
        initial={{ height: defaultOpen ? 'auto' : 0 }}
        animate={{
          height: isOpen ? 'auto' : 0,
        }}
        transition={{
          duration: 0.2,
          ease: 'easeInOut'
        }}
      >
        <div className="space-y-1">
          {children}
        </div>
      </motion.div>
    </div>
  );
}

export function HubSidebar({
  hubId,
  canModerate = false,
  canEdit = false,
  isCollapsed = false,
  onToggleCollapse,
}: HubSidebarProps) {
  const pathname = usePathname();

  // Simplified configuration
  const sidebarConfig: SidebarSection[] = [
    {
      key: 'main',
      items: [
        {
          value: 'overview',
          label: 'Overview',
          color: 'default',
          icon: MessageSquare,
          href: `/dashboard/hubs/${hubId}`,
          show: true,
        },
        {
          value: 'discovery',
          label: 'Discoverability',
          color: 'yellow',
          icon: Globe,
          href: `/dashboard/hubs/${hubId}/discoverability`,
          show: ({ canEdit }) => canEdit,
        },
      ],
    },
    {
      key: 'management',
      title: 'Management',
      defaultOpen: true,
      items: [
        {
          value: 'edit',
          label: 'Edit Hub',
          color: 'blue',
          icon: Edit,
          href: `/dashboard/hubs/${hubId}/edit`,
          show: ({ canEdit }) => canEdit,
        },
        {
          value: 'members',
          label: 'Members',
          color: 'blue',
          icon: Users,
          href: `/dashboard/hubs/${hubId}/members`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'connections',
          label: 'Connections',
          color: 'green',
          icon: Home,
          href: `/dashboard/hubs/${hubId}/connections`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'logging',
          label: 'Logging',
          color: 'purple',
          icon: FileText,
          href: `/dashboard/hubs/${hubId}/logging`,
          show: ({ canEdit }) => canEdit,
        },
      ],
    },
    {
      key: 'moderation',
      title: 'Moderation',
      defaultOpen: true,
      items: [
        {
          value: 'reports',
          label: 'Reports',
          color: 'red',
          icon: Shield,
          href: `/dashboard/hubs/${hubId}/reports`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'appeals',
          label: 'Appeals',
          color: 'orange',
          icon: Bell,
          href: `/dashboard/hubs/${hubId}/appeals`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'infractions',
          label: 'Infractions',
          color: 'orange',
          icon: Gavel,
          href: `/dashboard/hubs/${hubId}/infractions`,
          show: ({ canModerate }) => canModerate,
        },
        {
          value: 'anti-swear',
          label: 'Anti-Swear',
          color: 'red',
          icon: AlertTriangle,
          href: `/dashboard/hubs/${hubId}/anti-swear`,
          show: ({ canModerate }) => canModerate,
        },
      ],
    },
    {
      key: 'misc',
      items: [
        {
          value: 'settings',
          label: 'Settings',
          color: 'default',
          icon: Settings,
          href: `/dashboard/hubs/${hubId}/settings`,
          show: true,
        },
      ],
    },
  ];

  const permissions = { canModerate, canEdit };

  // Filter sections based on permissions
  const visibleSections = sidebarConfig
    .map((section) => ({
      ...section,
      items: section.items.filter((item) =>
        typeof item.show === 'function' ? item.show(permissions) : item.show,
      ),
    }))
    .filter((section) => section.items.length > 0);

  return (
    <div
      className={cn(
        'flex flex-col h-full bg-gray-900/95 border-r border-gray-800 transition-all duration-300',
        isCollapsed ? 'w-16' : 'w-64'
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
        {!isCollapsed && (
          <h2 className="text-sm font-semibold text-white flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full" />
            Hub Dashboard
          </h2>
        )}
        {onToggleCollapse && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleCollapse}
            className="h-8 w-8 text-gray-400 hover:text-white hover:bg-gray-800 shrink-0"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto p-3 space-y-6">
        {visibleSections.map((section) => {
          const sectionItems = section.items.map((item) => (
            <SidebarNavItem
              key={item.value}
              href={item.href}
              icon={item.icon}
              label={item.label}
              active={pathname === item.href}
              isCollapsed={isCollapsed}
              color={item.color}
            />
          ));

          // Render items directly if no title
          if (!section.title) {
            return (
              <div key={section.key} className="space-y-1">
                {sectionItems}
              </div>
            );
          }

          // Render as collapsible section
          return (
            <SidebarSection
              key={section.key}
              title={section.title}
              isCollapsed={isCollapsed}
              defaultOpen={section.defaultOpen}
            >
              {sectionItems}
            </SidebarSection>
          );
        })}
      </div>
    </div>
  );
}