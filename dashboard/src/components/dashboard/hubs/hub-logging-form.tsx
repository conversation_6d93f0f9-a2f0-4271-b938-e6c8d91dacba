"use client";

import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import {
    AlertCircle,
    AlertTriangle,
    Bell,
    Check,
    Flag,
    Info,
    MessageSquare,
    Save,
    Shield,
    Users
} from "lucide-react";
import { useState } from "react";
import { useCallback } from "react";
import { DiscordChannelSelector } from "./discord-channel-selector";
import { DiscordRoleSelector } from "./discord-role-selector";

interface LogConfig {
  id?: string;
  hubId: string;
  modLogsChannelId?: string | null;
  modLogsRoleId?: string | null;
  joinLeavesChannelId?: string | null;
  joinLeavesRoleId?: string | null;
  appealsChannelId?: string | null;
  appealsRoleId?: string | null;
  reportsChannelId?: string | null;
  reportsRoleId?: string | null;
  networkAlertsChannelId?: string | null;
  networkAlertsRoleId?: string | null;
  messageModerationChannelId?: string | null;
  messageModerationRoleId?: string | null;
}

interface HubLoggingFormProps {
  hubId: string;
  initialLogConfig: LogConfig | null;
}

export function HubLoggingForm({
  hubId,
  initialLogConfig,
}: HubLoggingFormProps) {
  const { toast } = useToast();
  const [logConfig, setLogConfig] = useState<LogConfig>(
    initialLogConfig || {
      hubId,
      modLogsChannelId: null,
      modLogsRoleId: null,
      joinLeavesChannelId: null,
      joinLeavesRoleId: null,
      appealsChannelId: null,
      appealsRoleId: null,
      reportsChannelId: null,
      reportsRoleId: null,
      networkAlertsChannelId: null,
      networkAlertsRoleId: null,
      messageModerationChannelId: null,
      messageModerationRoleId: null,
    }
  );
  const [serverIds, setServerIds] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState("modLogs");

  // Helper to check if the config has changed
  const checkForChanges = useCallback((newConfig: LogConfig) => {
    if (!initialLogConfig) return true;

    const keys = [
      "modLogsChannelId", "modLogsRoleId",
      "joinLeavesChannelId", "joinLeavesRoleId",
      "appealsChannelId", "appealsRoleId",
      "reportsChannelId", "reportsRoleId",
      "networkAlertsChannelId", "networkAlertsRoleId",
      "messageModerationChannelId", "messageModerationRoleId"
    ];

    return keys.some(key => {
      const initialValue = initialLogConfig[key as keyof LogConfig];
      const newValue = newConfig[key as keyof LogConfig];
      return initialValue !== newValue;
    });
  }, [initialLogConfig]);

  // Update a specific log type configuration
  const updateLogConfig = useCallback((
    logType: string,
    field: "channelId" | "roleId",
    value: string
  ) => {
    const fieldName = `${logType}${field.charAt(0).toUpperCase() + field.slice(1)}` as keyof LogConfig;
    const newConfig = { ...logConfig, [fieldName]: value || null };
    setLogConfig(newConfig);
    setHasChanges(checkForChanges(newConfig));
  }, [logConfig, checkForChanges]);

  // Handle server changes for a specific log type
  const handleServerChange = useCallback((logType: string, serverId: string) => {
    setServerIds(prev => ({ ...prev, [logType]: serverId }));

    // Clear the role selection when server changes
    const roleFieldName = `${logType}RoleId` as keyof LogConfig;
    if (logConfig[roleFieldName]) {
      updateLogConfig(logType, "roleId", "");
    }
  }, [logConfig, updateLogConfig]);

  const handleSaveLogConfig = async () => {
    if (!hasChanges) return;

    setIsSaving(true);
    try {
      const response = await fetch(`/api/hubs/${hubId}/logging`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          logConfig,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to save logging configuration");
      }

      toast({
        title: "Logging configuration saved",
        description: "Hub logging settings have been updated successfully.",
      });

      // Update initial values to reflect the new state
      setHasChanges(false);
    } catch (error) {
      console.error("Error saving logging configuration:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to save logging configuration",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Get the status of a log type (Configured or Not Configured)
  const getLogTypeStatus = (logType: string) => {
    const channelId = logConfig[`${logType}ChannelId` as keyof LogConfig];
    return channelId ? "Configured" : "Not Configured";
  };

  // Get the icon for a log type
  const getLogTypeIcon = (logType: string) => {
    switch (logType) {
      case "modLogs":
        return <Shield className="h-5 w-5 text-indigo-400" />;
      case "joinLeaves":
        return <Users className="h-5 w-5 text-green-400" />;
      case "appeals":
        return <MessageSquare className="h-5 w-5 text-blue-400" />;
      case "reports":
        return <Flag className="h-5 w-5 text-red-400" />;
      case "networkAlerts":
        return <Bell className="h-5 w-5 text-amber-400" />;
      case "messageModeration":
        return <MessageSquare className="h-5 w-5 text-purple-400" />;
      default:
        return <Info className="h-5 w-5 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Info Alert */}
      <Alert variant="default" className="bg-gradient-to-r from-blue-950/40 to-indigo-950/40 border-blue-500/30 backdrop-blur-sm">
        <Info className="h-5 w-5 text-blue-400" />
        <AlertTitle className="text-blue-300 font-semibold">How Logging Works</AlertTitle>
        <AlertDescription className="text-gray-300 mt-2">
          Configure Discord channels to receive real-time notifications about different hub activities.
          Each log type provides specific information to help you monitor and manage your hub effectively.
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="relative mb-8 overflow-x-auto no-scrollbar">
              <TabsList className="flex w-full min-w-max space-x-3 bg-transparent p-1 rounded-[var(--radius-button)]">
                <TabsTrigger
                  value="modLogs"
                  className="flex-1 min-w-[130px] sm:min-w-[150px] relative rounded-[var(--radius)] border border-gray-800/50 bg-gray-900/60 px-4 py-4 transition-all duration-300
                    data-[state=active]:border-indigo-500/60 data-[state=active]:bg-gradient-to-br data-[state=active]:from-indigo-950/50 data-[state=active]:to-indigo-900/30 data-[state=active]:text-indigo-300 data-[state=active]:shadow-lg data-[state=active]:shadow-indigo-500/20
                    hover:bg-gray-800/80 hover:border-gray-700/60 hover:scale-[1.02]"
                >
                  <div className="flex flex-col items-center space-y-2">
                    <Shield className="h-5 w-5" />
                    <span className="text-xs font-medium">Moderation</span>
                    {getLogTypeStatus("modLogs") === "Configured" && (
                      <div className="absolute top-2 right-2 h-2.5 w-2.5 rounded-full bg-emerald-400 shadow-lg shadow-emerald-400/50" />
                    )}
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="joinLeaves"
                  className="flex-1 min-w-[130px] sm:min-w-[150px] relative rounded-[var(--radius)] border border-gray-800/50 bg-gray-900/60 px-4 py-4 transition-all duration-300
                    data-[state=active]:border-emerald-500/60 data-[state=active]:bg-gradient-to-br data-[state=active]:from-emerald-950/50 data-[state=active]:to-emerald-900/30 data-[state=active]:text-emerald-300 data-[state=active]:shadow-lg data-[state=active]:shadow-emerald-500/20
                    hover:bg-gray-800/80 hover:border-gray-700/60 hover:scale-[1.02]"
                >
                  <div className="flex flex-col items-center space-y-2">
                    <Users className="h-5 w-5" />
                    <span className="text-xs font-medium">Join/Leave</span>
                    {getLogTypeStatus("joinLeaves") === "Configured" && (
                      <div className="absolute top-2 right-2 h-2.5 w-2.5 rounded-full bg-emerald-400 shadow-lg shadow-emerald-400/50" />
                    )}
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="appeals"
                  className="flex-1 min-w-[130px] sm:min-w-[150px] relative rounded-[var(--radius)] border border-gray-800/50 bg-gray-900/60 px-4 py-4 transition-all duration-300
                    data-[state=active]:border-blue-500/60 data-[state=active]:bg-gradient-to-br data-[state=active]:from-blue-950/50 data-[state=active]:to-blue-900/30 data-[state=active]:text-blue-300 data-[state=active]:shadow-lg data-[state=active]:shadow-blue-500/20
                    hover:bg-gray-800/80 hover:border-gray-700/60 hover:scale-[1.02]"
                >
                  <div className="flex flex-col items-center space-y-2">
                    <MessageSquare className="h-5 w-5" />
                    <span className="text-xs font-medium">Appeals</span>
                    {getLogTypeStatus("appeals") === "Configured" && (
                      <div className="absolute top-2 right-2 h-2.5 w-2.5 rounded-full bg-emerald-400 shadow-lg shadow-emerald-400/50" />
                    )}
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="reports"
                  className="flex-1 min-w-[130px] sm:min-w-[150px] relative rounded-[var(--radius)] border border-gray-800/50 bg-gray-900/60 px-4 py-4 transition-all duration-300
                    data-[state=active]:border-red-500/60 data-[state=active]:bg-gradient-to-br data-[state=active]:from-red-950/50 data-[state=active]:to-red-900/30 data-[state=active]:text-red-300 data-[state=active]:shadow-lg data-[state=active]:shadow-red-500/20
                    hover:bg-gray-800/80 hover:border-gray-700/60 hover:scale-[1.02]"
                >
                  <div className="flex flex-col items-center space-y-2">
                    <Flag className="h-5 w-5" />
                    <span className="text-xs font-medium">Reports</span>
                    {getLogTypeStatus("reports") === "Configured" && (
                      <div className="absolute top-2 right-2 h-2.5 w-2.5 rounded-full bg-emerald-400 shadow-lg shadow-emerald-400/50" />
                    )}
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="networkAlerts"
                  className="flex-1 min-w-[130px] sm:min-w-[150px] relative rounded-[var(--radius)] border border-gray-800/50 bg-gray-900/60 px-4 py-4 transition-all duration-300
                    data-[state=active]:border-amber-500/60 data-[state=active]:bg-gradient-to-br data-[state=active]:from-amber-950/50 data-[state=active]:to-amber-900/30 data-[state=active]:text-amber-300 data-[state=active]:shadow-lg data-[state=active]:shadow-amber-500/20
                    hover:bg-gray-800/80 hover:border-gray-700/60 hover:scale-[1.02]"
                >
                  <div className="flex flex-col items-center space-y-2">
                    <Bell className="h-5 w-5" />
                    <span className="text-xs font-medium">Alerts</span>
                    {getLogTypeStatus("networkAlerts") === "Configured" && (
                      <div className="absolute top-2 right-2 h-2.5 w-2.5 rounded-full bg-emerald-400 shadow-lg shadow-emerald-400/50" />
                    )}
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="messageModeration"
                  className="flex-1 min-w-[130px] sm:min-w-[150px] relative rounded-[var(--radius)] border border-gray-800/50 bg-gray-900/60 px-4 py-4 transition-all duration-300
                    data-[state=active]:border-purple-500/60 data-[state=active]:bg-gradient-to-br data-[state=active]:from-purple-950/50 data-[state=active]:to-purple-900/30 data-[state=active]:text-purple-300 data-[state=active]:shadow-lg data-[state=active]:shadow-purple-500/20
                    hover:bg-gray-800/80 hover:border-gray-700/60 hover:scale-[1.02]"
                >
                  <div className="flex flex-col items-center space-y-2">
                    <MessageSquare className="h-5 w-5" />
                    <span className="text-xs font-medium">Messages</span>
                    {getLogTypeStatus("messageModeration") === "Configured" && (
                      <div className="absolute top-2 right-2 h-2.5 w-2.5 rounded-full bg-emerald-400 shadow-lg shadow-emerald-400/50" />
                    )}
                  </div>
                </TabsTrigger>
              </TabsList>
            </div>

            {["modLogs", "joinLeaves", "appeals", "reports", "networkAlerts", "messageModeration"].map((logType) => (
              <TabsContent key={logType} value={logType} className="space-y-6 mt-8">
                <Card className="premium-card overflow-hidden group hover:scale-[1.01] transition-all duration-300">
                  <div className={`h-1 w-full bg-gradient-to-r ${
                    logType === "modLogs" ? "from-indigo-500 to-indigo-600" :
                    logType === "joinLeaves" ? "from-emerald-500 to-emerald-600" :
                    logType === "appeals" ? "from-blue-500 to-blue-600" :
                    logType === "reports" ? "from-red-500 to-red-600" :
                    logType === "networkAlerts" ? "from-amber-500 to-amber-600" :
                    "from-purple-500 to-purple-600"
                  }`} />
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-[var(--radius-button)] ${
                          logType === "modLogs" ? "bg-indigo-500/20" :
                          logType === "joinLeaves" ? "bg-emerald-500/20" :
                          logType === "appeals" ? "bg-blue-500/20" :
                          logType === "reports" ? "bg-red-500/20" :
                          logType === "networkAlerts" ? "bg-amber-500/20" :
                          "bg-purple-500/20"
                        }`}>
                          {getLogTypeIcon(logType)}
                        </div>
                        <div>
                          <CardTitle className="text-xl font-semibold text-white">
                            {logType === "modLogs" ? "Moderation Logs" :
                             logType === "joinLeaves" ? "Join/Leave Logs" :
                             logType === "appeals" ? "Appeal Logs" :
                             logType === "reports" ? "Report Logs" :
                             logType === "networkAlerts" ? "Network Alert Logs" :
                             "Message Moderation Logs"}
                          </CardTitle>
                          <CardDescription className="text-gray-400 mt-1">
                            {logType === "modLogs" ? "Track all moderation actions like blacklisting and warnings" :
                             logType === "joinLeaves" ? "Monitor servers joining or leaving your hub" :
                             logType === "appeals" ? "Receive notifications about blacklist appeal requests" :
                             logType === "reports" ? "Get notified when content is reported by users" :
                             logType === "networkAlerts" ? "Receive important system notifications and alerts" :
                             "Log message deletions, edits, and other message moderation actions"}
                          </CardDescription>
                        </div>
                      </div>
                      <div className={`px-3 py-1.5 rounded-[var(--radius-badge)] text-xs font-medium ${
                        getLogTypeStatus(logType) === "Configured"
                          ? "bg-emerald-500/20 text-emerald-400 border border-emerald-500/30"
                          : "bg-gray-500/20 text-gray-400 border border-gray-500/30"
                      }`}>
                        {getLogTypeStatus(logType)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <DiscordChannelSelector
                          hubId={hubId}
                          value={logConfig[`${logType}ChannelId` as keyof LogConfig] as string || ""}
                          onChange={(value: string) => updateLogConfig(logType, "channelId", value)}
                          onServerChange={(serverId: string) => handleServerChange(logType, serverId)}
                          label="Channel"
                          placeholder="Select a Discord channel"
                          description={`The Discord channel where ${
                            logType === "modLogs" ? "moderation logs" :
                            logType === "joinLeaves" ? "join/leave notifications" :
                            logType === "appeals" ? "appeal requests" :
                            logType === "reports" ? "user reports" :
                            "system alerts"
                          } will be sent`}
                        />
                      </div>

                      <div className="space-y-2">
                        <DiscordRoleSelector
                          hubId={hubId}
                          serverId={serverIds[logType]}
                          value={logConfig[`${logType}RoleId` as keyof LogConfig] as string || ""}
                          onChange={(value: string) => updateLogConfig(logType, "roleId", value)}
                          label="Role (Optional)"
                          placeholder="Select a Discord role to mention"
                          description={`A role to mention when ${
                            logType === "modLogs" ? "moderation actions occur" :
                            logType === "joinLeaves" ? "servers join or leave" :
                            logType === "appeals" ? "appeals are received" :
                            logType === "reports" ? "content is reported" :
                            logType === "networkAlerts" ? "important alerts are sent" :
                            "message moderation actions occur"
                          }`}
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="bg-gray-900/30 border-t border-gray-800/50 flex justify-between pt-4">
                    <div className="text-xs text-gray-500 flex items-center">
                      <AlertCircle className="h-3.5 w-3.5 mr-1 text-gray-500" />
                      Make sure the bot has permission to send messages in this channel
                    </div>
                  </CardFooter>
                </Card>
              </TabsContent>
            ))}
          </Tabs>

          {/* Enhanced Save Button */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-800/50">
            <div className="text-sm text-gray-400">
              {hasChanges ? "You have unsaved changes" : "All changes saved"}
            </div>
            <Button
              className={`px-8 py-2.5 font-semibold transition-all duration-300 ${
                hasChanges
                  ? "bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 shadow-lg shadow-indigo-500/25"
                  : "bg-emerald-500/20 text-emerald-400 border border-emerald-500/30 hover:bg-emerald-500/30"
              }`}
              onClick={handleSaveLogConfig}
              disabled={!hasChanges || isSaving}
            >
              {isSaving ? (
                <>
                  <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full" />
                  Saving...
                </>
              ) : (
                <>
                  {hasChanges ? (
                    <Save className="h-4 w-4 mr-2" />
                  ) : (
                    <Check className="h-4 w-4 mr-2" />
                  )}
                  {hasChanges ? "Save Configuration" : "Configuration Saved"}
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Enhanced Tips Sidebar */}
        <div className="lg:col-span-1 space-y-6">
          <Card className="premium-card sticky top-6">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <div className="p-2 bg-amber-500/20 rounded-[var(--radius-button)]">
                  <Info className="h-4 w-4 text-amber-400" />
                </div>
                Logging Best Practices
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="p-1.5 bg-blue-500/20 rounded-[var(--radius)] mt-0.5">
                    <Shield className="h-3.5 w-3.5 text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold text-gray-200">Channel Security</h3>
                    <p className="text-xs text-gray-400 mt-1">
                      Use private channels for sensitive logs like moderation actions and user reports.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="p-1.5 bg-purple-500/20 rounded-[var(--radius)] mt-0.5">
                    <Bell className="h-3.5 w-3.5 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold text-gray-200">Smart Notifications</h3>
                    <p className="text-xs text-gray-400 mt-1">
                      Create dedicated roles for different log types to avoid notification spam.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="p-1.5 bg-emerald-500/20 rounded-[var(--radius)] mt-0.5">
                    <Check className="h-3.5 w-3.5 text-emerald-400" />
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold text-gray-200">Bot Permissions</h3>
                    <p className="text-xs text-gray-400 mt-1">
                      Ensure InterChat can view and send messages in all configured channels.
                    </p>
                  </div>
                </div>
              </div>

              <Alert variant="default" className="bg-gradient-to-r from-amber-950/40 to-orange-950/40 border-amber-500/30">
                <AlertTriangle className="h-4 w-4 text-amber-400" />
                <AlertTitle className="text-amber-300 text-sm">Security Reminder</AlertTitle>
                <AlertDescription className="text-gray-300 text-xs mt-1">
                  Always verify channel permissions before saving. Misconfigured channels may cause logging failures.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}