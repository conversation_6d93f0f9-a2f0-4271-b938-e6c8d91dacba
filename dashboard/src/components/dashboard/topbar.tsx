"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Home, Menu, Scale, Settings } from "lucide-react";
import type { User } from "next-auth";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { UserNav } from "../user-nav";
import { MobileSidebar } from "./mobile-sidebar";
import { NotificationDropdown } from "./notifications/notification-dropdown";
import { OnboardingHelpMenu } from "./onboarding/onboarding-help-menu";

export function DashboardTopBar({ user }: { user: User }) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  return (
    <div className="sticky top-0 z-40 flex h-16 flex-shrink-0 border-b border-white/10 bg-gradient-to-r from-gray-900/95 via-gray-900/90 to-gray-950/95 backdrop-blur-xl">
      {/* Enhanced background overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-900/5 via-transparent to-blue-900/5" />

      <div className="flex flex-1 items-center px-4 relative z-10">
        {/* Enhanced Logo and Brand */}
        <div className="flex items-center gap-3">
          <Link href="/" className="flex items-center gap-3 group">
            <div className="relative">
              <Image
                alt="InterChat"
                src="/interchat.png"
                height={32}
                width={32}
                className="rounded-[var(--radius-avatar)] border border-white/20 group-hover:border-purple-400/50 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-purple-500/20"
              />
              {/* Subtle glow effect */}
              <div className="absolute inset-0 rounded-[var(--radius-avatar)] bg-gradient-to-r from-purple-400/20 to-blue-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10" />
            </div>
            <span className="font-bold text-lg bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-400 to-blue-400 hidden sm:block group-hover:from-purple-300 group-hover:via-indigo-300 group-hover:to-blue-300 transition-all duration-300">
              InterChat
            </span>
          </Link>
        </div>

        {/* Enhanced Navigation Links - Hidden on mobile */}
        <div className="hidden lg:flex items-center gap-2 ml-8">
          <Link
            href="/dashboard"
            className={cn(
              "flex items-center gap-2 px-4 py-2.5 text-sm rounded-[var(--radius-button)] transition-all duration-300 font-medium",
              pathname === "/dashboard"
                ? "bg-gradient-to-r from-purple-500/20 to-blue-500/20 text-purple-300 border border-purple-500/30 shadow-lg shadow-purple-500/10"
                : "text-gray-300 hover:text-white hover:bg-white/5 border border-transparent hover:border-white/10"
            )}
          >
            <Home className="h-4 w-4" />
            Dashboard
          </Link>
          <Link
            href="/dashboard/my-appeals"
            className={cn(
              "flex items-center gap-2 px-4 py-2.5 text-sm rounded-[var(--radius-button)] transition-all duration-300 font-medium",
              pathname.startsWith("/dashboard/my-appeals")
                ? "bg-gradient-to-r from-purple-500/20 to-blue-500/20 text-purple-300 border border-purple-500/30 shadow-lg shadow-purple-500/10"
                : "text-gray-300 hover:text-white hover:bg-white/5 border border-transparent hover:border-white/10"
            )}
          >
            <Scale className="h-4 w-4" />
            My Appeals
          </Link>
          <Link
            href="/dashboard/settings"
            className={cn(
              "flex items-center gap-2 px-4 py-2.5 text-sm rounded-[var(--radius-button)] transition-all duration-300 font-medium",
              pathname.startsWith("/dashboard/settings")
                ? "bg-gradient-to-r from-purple-500/20 to-blue-500/20 text-purple-300 border border-purple-500/30 shadow-lg shadow-purple-500/10"
                : "text-gray-300 hover:text-white hover:bg-white/5 border border-transparent hover:border-white/10"
            )}
          >
            <Settings className="h-4 w-4" />
            Settings
          </Link>
        </div>

        {/* Enhanced Mobile menu button */}
        <div className="flex items-center lg:hidden ml-auto">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="text-gray-300 hover:text-white hover:bg-white/10 border border-transparent hover:border-white/20 transition-all duration-300"
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Open menu</span>
          </Button>
        </div>

        {/* Enhanced Right side actions */}
        <div className="hidden lg:flex items-center gap-3 ml-auto">
          {/* Notifications with enhanced styling */}
          <div data-tour="notifications" className="relative">
            <div className="p-1 rounded-[var(--radius-button)] hover:bg-white/5 transition-all duration-300">
              <NotificationDropdown />
            </div>
          </div>

          {/* Help with enhanced styling */}
          <div className="p-1 rounded-[var(--radius-button)] hover:bg-white/5 transition-all duration-300">
            <OnboardingHelpMenu />
          </div>

          {/* User dropdown with enhanced styling */}
          <div className="flex items-center" data-tour="user-menu">
            <div className="p-1 rounded-[var(--radius-button)] hover:bg-white/5 transition-all duration-300">
              <UserNav
                user={user}
                firstPage={{ name: "Home", icon: Home, href: "/" }}
              />
            </div>
          </div>
        </div>

        {/* Mobile sidebar using portal to render outside the DOM hierarchy */}
        <MobileSidebar
          isOpen={isMobileMenuOpen}
          onClose={() => setIsMobileMenuOpen(false)}
          user={user}
        />
      </div>
    </div>
  );
}
