"use client";
import React from "react";
import { motion } from "framer-motion";
import { 
  BarChart3, 
  MessageSquare, 
  Server, 
  Users, 
  Activity, 
  Zap, 
  Shield, 
  Globe, 
  Bell 
} from "lucide-react";

// Map of icon names to components
const IconMap = {
  BarChart3,
  MessageSquare,
  Server,
  Users,
  Activity,
  Zap,
  Shield,
  Globe,
  Bell,
};

type IconName = keyof typeof IconMap;

interface StatCardProps {
  title: string;
  value: string;
  description: string;
  iconName: IconName;
  index: number;
  color?: "purple" | "blue" | "indigo" | "pink" | "emerald" | "orange";
}

export function StatCard({
  title,
  value,
  description,
  iconName,
  index,
  color = "purple",
}: StatCardProps) {
  const Icon = IconMap[iconName];

  // Helper function to get the primary color for background decoration
  const getPrimaryColor = (colorName: string) => {
    const colorMap: Record<string, string> = {
      purple: 'var(--color-brand-purple-500)',
      blue: 'var(--color-brand-blue-500)',
      indigo: 'var(--color-brand-indigo-500)',
      pink: 'var(--color-brand-pink-500)',
      emerald: 'var(--color-brand-emerald-500)',
      orange: 'var(--color-brand-orange-500)',
    };
    return colorMap[colorName] || colorMap.purple;
  };

  const colorVariants = {
    purple: {
      gradient: "from-[var(--color-brand-purple-500)]/20 via-[var(--color-brand-purple-600)]/10 to-[var(--color-brand-purple-700)]/20",
      border: "border-[var(--color-brand-purple-400)]/30",
      iconBg: "bg-gradient-to-br from-[var(--color-brand-purple-500)] to-[var(--color-brand-purple-600)]",
      iconGlow: "shadow-[var(--shadow-glow-purple)]",
      textAccent: "text-[var(--color-brand-purple-400)]",
      valueGradient: "bg-gradient-to-r from-[var(--color-brand-purple-400)] to-[var(--color-brand-purple-500)] bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-[var(--shadow-glow-purple)]",
      progressGradient: "from-[var(--color-brand-purple-500)] to-[var(--color-brand-purple-600)]"
    },
    blue: {
      gradient: "from-[var(--color-brand-blue-500)]/20 via-[var(--color-brand-blue-600)]/10 to-[var(--color-brand-blue-600)]/20",
      border: "border-[var(--color-brand-blue-400)]/30",
      iconBg: "bg-gradient-to-br from-[var(--color-brand-blue-500)] to-[var(--color-brand-blue-600)]",
      iconGlow: "shadow-[var(--shadow-glow-blue)]",
      textAccent: "text-[var(--color-brand-blue-400)]",
      valueGradient: "bg-gradient-to-r from-[var(--color-brand-blue-400)] to-[var(--color-brand-blue-500)] bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-[var(--shadow-glow-blue)]",
      progressGradient: "from-[var(--color-brand-blue-500)] to-[var(--color-brand-blue-600)]"
    },
    indigo: {
      gradient: "from-[var(--color-brand-indigo-500)]/20 via-[var(--color-brand-indigo-600)]/10 to-[var(--color-brand-indigo-600)]/20",
      border: "border-[var(--color-brand-indigo-400)]/30",
      iconBg: "bg-gradient-to-br from-[var(--color-brand-indigo-500)] to-[var(--color-brand-indigo-600)]",
      iconGlow: "shadow-[var(--shadow-glow-indigo)]",
      textAccent: "text-[var(--color-brand-indigo-400)]",
      valueGradient: "bg-gradient-to-r from-[var(--color-brand-indigo-400)] to-[var(--color-brand-indigo-500)] bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-[var(--shadow-glow-indigo)]",
      progressGradient: "from-[var(--color-brand-indigo-500)] to-[var(--color-brand-indigo-600)]"
    },
    pink: {
      gradient: "from-[var(--color-brand-pink-500)]/20 via-[var(--color-brand-pink-600)]/10 to-[var(--color-brand-pink-600)]/20",
      border: "border-[var(--color-brand-pink-400)]/30",
      iconBg: "bg-gradient-to-br from-[var(--color-brand-pink-500)] to-[var(--color-brand-pink-600)]",
      iconGlow: "shadow-[var(--shadow-glow-pink)]",
      textAccent: "text-[var(--color-brand-pink-400)]",
      valueGradient: "bg-gradient-to-r from-[var(--color-brand-pink-400)] to-[var(--color-brand-pink-500)] bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-[var(--shadow-glow-pink)]",
      progressGradient: "from-[var(--color-brand-pink-500)] to-[var(--color-brand-pink-600)]"
    },
    emerald: {
      gradient: "from-[var(--color-brand-emerald-500)]/20 via-[var(--color-brand-emerald-600)]/10 to-[var(--color-brand-emerald-600)]/20",
      border: "border-[var(--color-brand-emerald-400)]/30",
      iconBg: "bg-gradient-to-br from-[var(--color-brand-emerald-500)] to-[var(--color-brand-emerald-600)]",
      iconGlow: "shadow-[var(--shadow-glow-emerald)]",
      textAccent: "text-[var(--color-brand-emerald-400)]",
      valueGradient: "bg-gradient-to-r from-[var(--color-brand-emerald-400)] to-[var(--color-brand-emerald-500)] bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-[var(--shadow-glow-emerald)]",
      progressGradient: "from-[var(--color-brand-emerald-500)] to-[var(--color-brand-emerald-600)]"
    },
    orange: {
      gradient: "from-[var(--color-brand-orange-500)]/20 via-[var(--color-brand-orange-600)]/10 to-[var(--color-brand-orange-600)]/20",
      border: "border-[var(--color-brand-orange-400)]/30",
      iconBg: "bg-gradient-to-br from-[var(--color-brand-orange-500)] to-[var(--color-brand-orange-600)]",
      iconGlow: "shadow-[var(--shadow-glow-orange)]",
      textAccent: "text-[var(--color-brand-orange-400)]",
      valueGradient: "bg-gradient-to-r from-[var(--color-brand-orange-400)] to-[var(--color-brand-orange-500)] bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-[var(--shadow-glow-orange)]",
      progressGradient: "from-[var(--color-brand-orange-500)] to-[var(--color-brand-orange-600)]"
    },
  };

  const variant = colorVariants[color];

  return (
    <motion.div
      initial={{ opacity: 0, y: 30, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      whileHover={{ 
        y: -8, 
        scale: 1.03,
        transition: { duration: 0.2, type: "spring", stiffness: 300 }
      }}
      className="h-full group"
    >
      <div
        className={`
          relative h-full p-6 border backdrop-blur-xl
          bg-gradient-to-br ${variant.gradient}
          ${variant.border} ${variant.hoverGlow}
          transition-all duration-300 ease-out
          hover:shadow-2xl hover:border-opacity-50
          before:absolute before:inset-0
          before:bg-gradient-to-br before:from-white/5 before:to-transparent
          before:pointer-events-none
          overflow-hidden
        `}
        style={{
          borderRadius: 'var(--radius)', // 8px for cards
          background: `
            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
            var(--bg-gradient-primary)
          `
        }}
      >
        {/* Background decoration */}
        <div
          className="absolute -top-4 -right-4 w-24 h-24 rounded-full opacity-10 blur-xl"
          style={{
            background: `linear-gradient(135deg, ${getPrimaryColor(color)}, transparent)`
          }}
        />

        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div className="space-y-2 flex-1">
            <h3 className="text-sm font-bold text-gray-300 uppercase tracking-widest">
              {title}
            </h3>
            <p className="text-xs text-gray-400 leading-relaxed max-w-[200px]">
              {description}
            </p>
          </div>
          
          {/* Icon with glow effect */}
          <motion.div
            className={`
              relative p-3 ${variant.iconBg} ${variant.iconGlow}
              shadow-lg group-hover:shadow-xl transition-all duration-300
            `}
            style={{ borderRadius: 'var(--radius-button)' }} // 12px for buttons
            whileHover={{ rotate: 5, scale: 1.1 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <Icon className="h-6 w-6 text-white" />
            <div
              className="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              style={{ borderRadius: 'var(--radius-button)' }}
            />
          </motion.div>
        </div>

        {/* Value */}
        <motion.div
          className="space-y-1"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
        >
          <div className={`text-5xl font-black tracking-tight ${variant.valueGradient}`}>
            {value}
          </div>
          
          {/* Subtle progress bar */}
          <div className="w-full h-1 bg-gray-800/50 rounded-full overflow-hidden">
            <motion.div
              className={`h-full bg-gradient-to-r ${variant.progressGradient}`}
              initial={{ width: 0 }}
              animate={{ width: "75%" }}
              transition={{ duration: 1, delay: index * 0.1 + 0.5 }}
            />
          </div>
        </motion.div>

        {/* Animated particles */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden" style={{ borderRadius: 'var(--radius)' }}>
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={`particle-${index}-${i}`}
              className="absolute w-1 h-1 bg-white/30 rounded-full"
              animate={{
                x: [0, 100, 0],
                y: [0, -50, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3,
                delay: i * 0.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              style={{
                left: `${20 + i * 30}%`,
                top: `${60 + i * 10}%`,
              }}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
}
