'use client';

import { motion } from 'motion/react';
import { useSearchParams } from 'next/navigation';
import { SearchBar } from './SearchBar';

/**
 * 1️⃣ Hero Section (Top) - Redesigned
 * Features headline, subtext, and large centered search bar
 */
export function HeroSection() {
  const searchParams = useSearchParams();

  // Get initial search term from URL params
  const initialSearchTerm = searchParams.get('search') || '';

  return (
    <div className="relative overflow-hidden py-24 sm:py-32">
      {/* Enhanced animated background elements */}
      <div className="absolute inset-0 opacity-40">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500/15 rounded-full blur-3xl animate-float" />
        <div className="absolute top-40 right-20 w-96 h-96 bg-blue-500/15 rounded-full blur-3xl animate-float" style={{ animationDelay: '1s' }} />
        <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-indigo-500/15 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
      </div>

      {/* Enhanced Grid Pattern Overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.03)_1px,transparent_1px)] bg-[size:60px_60px] [mask-image:radial-gradient(ellipse_80%_80%_at_50%_50%,#000_20%,transparent_120%)]" />

      <div className="container mx-auto px-4 py-20 relative z-10">
        <div className="max-w-6xl mx-auto text-center">
          {/* Enhanced Headline */}
          <motion.h1
            className="text-6xl md:text-8xl font-bold mb-8 bg-clip-text text-transparent bg-gradient-to-r from-white via-purple-200 to-blue-200 drop-shadow-2xl leading-tight"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            Discover Amazing{" "}
            <span className="bg-gradient-to-r from-purple-400 via-indigo-400 to-blue-400 bg-clip-text text-transparent animate-gradient-slow">
              Hubs
            </span>
          </motion.h1>

          {/* Enhanced Subtext */}
          <motion.p
            className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-16"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Connect with communities across Discord. Find the perfect hub for your interests and start meaningful conversations.
          </motion.p>

          {/* Enhanced 🔎 Search Bar (centered, large) */}
          <motion.div
            className="max-w-4xl mx-auto mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <div className="relative">
              <div className="premium-card p-2 rounded-2xl">
                <SearchBar
                  initialValue={initialSearchTerm}
                  placeholder="Search by hub name, topic, or tag..."
                  size="large"
                  variant="hero"
                  showButton={true}
                  autoFocus={false}
                />
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
